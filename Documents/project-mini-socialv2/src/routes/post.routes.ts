import express from 'express';
import { postController } from '../controllers/post.controller';
import { commentController } from '../controllers/comment.controller';
import { validate } from '../middlewares/validate';
import { 
  createPostSchema, 
  updatePostSchema, 
  getPostSchema,
  listPostsSchema
} from '../validators/post.validators';
import { 
  createCommentSchema,
  getCommentsSchema
} from '../validators/comment.validators';
import { authenticate } from '../middlewares/auth';

const router = express.Router();

// Create post
router.post('/', authenticate, validate(createPostSchema), postController.createPost);

// Get all posts (with filters)
router.get('/', validate(listPostsSchema), postController.listPosts);

// Get post by ID
router.get('/:id', validate(getPostSchema), postController.getPost);

// Update post
router.put('/:id', authenticate, validate(updatePostSchema), postController.updatePost);

// Delete post
router.delete('/:id', authenticate, validate(getPostSchema), postController.deletePost);

// Like post
router.post('/:id/like', authenticate, validate(getPostSchema), postController.likePost);

// Unlike post
router.delete('/:id/like', authenticate, validate(getPostSchema), postController.unlikePost);

// Add comment to post
router.post('/:id/comments', authenticate, validate(createCommentSchema), commentController.createComment);

// Get post comments
router.get('/:id/comments', validate(getCommentsSchema), commentController.getComments);

export default router;