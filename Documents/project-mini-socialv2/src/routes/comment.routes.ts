import express from 'express';
import { commentController } from '../controllers/comment.controller';
import { validate } from '../middlewares/validate';
import { updateCommentSchema, deleteCommentSchema } from '../validators/comment.validators';
import { authenticate } from '../middlewares/auth';

const router = express.Router();

// Update comment
router.put('/:id', authenticate, validate(updateCommentSchema), commentController.updateComment);

// Delete comment
router.delete('/:id', authenticate, validate(deleteCommentSchema), commentController.deleteComment);

// Like comment
router.post('/:id/like', authenticate, validate(deleteCommentSchema), commentController.likeComment);

// Unlike comment
router.delete('/:id/like', authenticate, validate(deleteCommentSchema), commentController.unlikeComment);

// Get comment replies
router.get('/:id/replies', commentController.getCommentReplies);

export default router;