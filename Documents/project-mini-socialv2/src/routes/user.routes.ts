import express from 'express';
import { user<PERSON>ontroller } from '../controllers/user.controller';
import { validate } from '../middlewares/validate';
import { updateUserSchema, userIdSchema, followUserSchema } from '../validators/user.validators';
import { authenticate } from '../middlewares/auth';

const router = express.Router();

// Get user by ID
router.get('/:id', validate(userIdSchema), userController.getUser);

// Update user
router.put('/:id', authenticate, validate(updateUserSchema), userController.updateUser);

// Follow user
router.post('/:id/follow', authenticate, validate(followUserSchema), userController.followUser);

// Unfollow user
router.delete('/:id/follow', authenticate, validate(followUserSchema), userController.unfollowUser);

// Search users
router.get('/', authenticate, userController.searchUsers);

export default router;