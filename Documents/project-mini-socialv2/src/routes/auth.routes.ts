import express from 'express';
import { authController } from '../controllers/auth.controller';
import { validate } from '../middlewares/validate';
import { registerSchema, loginSchema, refreshTokenSchema } from '../validators/auth.validators';
import { authenticate } from '../middlewares/auth';

const router = express.Router();

// Register new user
router.post('/register', validate(registerSchema), authController.register);

// Login user
router.post('/login', validate(loginSchema), authController.login);

// Refresh token
router.post('/refresh-token', validate(refreshTokenSchema), authController.refreshToken);

// Get current user
router.get('/me', authenticate, authController.getCurrentUser);

export default router;