import express from 'express';
import { feedController } from '../controllers/feed.controller';
import { authenticate } from '../middlewares/auth';

const router = express.Router();

// Get user feed
router.get('/', authenticate, feedController.getFeed);

// Get explore feed
router.get('/explore', authenticate, feedController.getExploreFeed);

// Get trending hashtags
router.get('/trending', feedController.getTrendingHashtags);

// Get posts by hashtag
router.get('/hashtags/:tag/posts', feedController.getHashtagPosts);

export default router;