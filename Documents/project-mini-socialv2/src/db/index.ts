import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { logger } from '../utils/logger';

// Create a PostgreSQL connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 2000, // How long to wait for a connection to be established
});

// Test database connection with retry
const connectWithRetry = async () => {
  const maxRetries = 5;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      const client = await pool.connect();
      client.release();
      logger.info('Database connected successfully');
      return;
    } catch (err) {
      retries++;
      logger.error(`Database connection error (attempt ${retries}/${maxRetries}):`, err instanceof Error ? err.message : String(err));

      if (retries >= maxRetries) {
        logger.error('Failed to connect to database after maximum retries');
        return;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
};

// Start connection attempt
connectWithRetry();

// Initialize Drizzle ORM with the connection pool
export const db = drizzle(pool);

// Export a method to close the database connection (useful for tests and graceful shutdown)
export const closeDatabase = async () => {
  await pool.end();
  logger.info('Database connection closed');
};