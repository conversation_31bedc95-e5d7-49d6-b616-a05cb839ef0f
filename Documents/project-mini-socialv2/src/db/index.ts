import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { logger } from '../utils/logger';

// Create a PostgreSQL connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 2000, // How long to wait for a connection to be established
});

// Test database connection
pool.connect()
  .then(() => logger.info('Database connected successfully'))
  .catch((err) => {
    logger.error('Database connection error:', err);
    process.exit(1);
  });

// Initialize Drizzle ORM with the connection pool
export const db = drizzle(pool);

// Export a method to close the database connection (useful for tests and graceful shutdown)
export const closeDatabase = async () => {
  await pool.end();
  logger.info('Database connection closed');
};