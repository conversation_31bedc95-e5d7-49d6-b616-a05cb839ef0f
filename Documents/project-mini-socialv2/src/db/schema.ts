import { pgTable, varchar, text, timestamp, boolean, integer, primaryKey, uuid, unique, json, index, foreignKey } from 'drizzle-orm/pg-core';

// Users Table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  username: varchar('username', { length: 50 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  role: varchar('role', { length: 100 }).default('user'),
  displayName: varchar('display_name', { length: 100 }),
  bio: text('bio'),
  profileImageUrl: varchar('profile_image_url', { length: 500 }),
  coverImageUrl: varchar('cover_image_url', { length: 500 }),
  isVerified: boolean('is_verified').default(false),
  isPrivate: boolean('is_private').default(false),
  isActive: boolean('is_active').default(true),
  followersCount: integer('followers_count').default(0),
  followingCount: integer('following_count').default(0),
  postsCount: integer('posts_count').default(0),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  deletedAt: timestamp('deleted_at')
}, (table) => {
  return {
    usernameIdx: index('idx_username').on(table.username),
    emailIdx: index('idx_email').on(table.email),
    createdAtIdx: index('idx_created_at').on(table.createdAt)
  };
});

// Posts Table
export const posts = pgTable('posts', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  content: text('content').notNull(),
  mediaUrls: json('media_urls').$type<string[]>(),
  likesCount: integer('likes_count').default(0),
  commentsCount: integer('comments_count').default(0),
  sharesCount: integer('shares_count').default(0),
  isPublic: boolean('is_public').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  deletedAt: timestamp('deleted_at')
}, (table) => {
  return {
    userIdIdx: index('idx_user_id').on(table.userId),
    createdAtIdx: index('idx_created_at').on(table.createdAt),
    userCreatedIdx: index('idx_user_created').on(table.userId, table.createdAt)
  };
});

// Likes Table
export const likes = pgTable('likes', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  postId: uuid('post_id').notNull().references(() => posts.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').defaultNow()
}, (table) => {
  return {
    postIdIdx: index('idx_post_id').on(table.postId),
    userIdIdx: index('idx_user_id').on(table.userId),
    uniqueUserPostLike: unique('unique_user_post_like').on(table.userId, table.postId)
  };
});

// Comments Table
export const comments = pgTable('comments', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  postId: uuid('post_id').notNull().references(() => posts.id, { onDelete: 'cascade' }),
  parentCommentId: uuid('parent_comment_id').references((): any => comments.id, { onDelete: 'cascade' }),
  content: text('content').notNull(),
  likesCount: integer('likes_count').default(0),
  repliesCount: integer('replies_count').default(0),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  deletedAt: timestamp('deleted_at')
}, (table) => {
  return {
    postIdIdx: index('idx_post_id').on(table.postId),
    userIdIdx: index('idx_user_id').on(table.userId),
    parentCommentIdx: index('idx_parent_comment').on(table.parentCommentId),
    postCreatedIdx: index('idx_post_created').on(table.postId, table.createdAt)
  };
});

// Comment Likes Table
export const commentLikes = pgTable('comment_likes', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  commentId: uuid('comment_id').notNull().references(() => comments.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').defaultNow()
}, (table) => {
  return {
    commentIdIdx: index('idx_comment_id').on(table.commentId),
    userIdIdx: index('idx_user_id').on(table.userId),
    uniqueUserCommentLike: unique('unique_user_comment_like').on(table.userId, table.commentId)
  };
});

// Follows Table
export const follows = pgTable('follows', {
  id: uuid('id').primaryKey().defaultRandom(),
  followerId: uuid('follower_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  followingId: uuid('following_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  status: varchar('status', { length: 20 }).notNull().default('accepted'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
}, (table) => {
  return {
    followerIdx: index('idx_follower').on(table.followerId),
    followingIdx: index('idx_following').on(table.followingId),
    statusIdx: index('idx_status').on(table.status),
    uniqueFollow: unique('unique_follow').on(table.followerId, table.followingId)
  };
});

// Reports Table
export const reports = pgTable('reports', {
  id: uuid('id').primaryKey().defaultRandom(),
  reporterId: uuid('reporter_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  reportedUserId: uuid('reported_user_id').references(() => users.id, { onDelete: 'set null' }),
  postId: uuid('post_id').references(() => posts.id, { onDelete: 'set null' }),
  commentId: uuid('comment_id').references(() => comments.id, { onDelete: 'set null' }),
  reportType: varchar('report_type', { length: 50 }).notNull(),
  description: text('description'),
  status: varchar('status', { length: 20 }).default('pending'),
  adminNotes: text('admin_notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
}, (table) => {
  return {
    reporterIdx: index('idx_reporter').on(table.reporterId),
    reportedUserIdx: index('idx_reported_user').on(table.reportedUserId),
    postIdx: index('idx_post').on(table.postId),
    commentIdx: index('idx_comment').on(table.commentId),
    statusIdx: index('idx_status').on(table.status),
    typeIdx: index('idx_type').on(table.reportType)
  };
});

// Notifications Table
export const notifications = pgTable('notifications', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  actorId: uuid('actor_id').references(() => users.id, { onDelete: 'set null' }),
  type: varchar('type', { length: 50 }).notNull(),
  postId: uuid('post_id').references(() => posts.id, { onDelete: 'set null' }),
  commentId: uuid('comment_id').references(() => comments.id, { onDelete: 'set null' }),
  message: text('message'),
  isRead: boolean('is_read').default(false),
  createdAt: timestamp('created_at').defaultNow()
}, (table) => {
  return {
    userIdIdx: index('idx_user_id').on(table.userId),
    actorIdIdx: index('idx_actor_id').on(table.actorId),
    isReadIdx: index('idx_is_read').on(table.isRead),
    createdAtIdx: index('idx_created_at').on(table.createdAt),
    userReadCreatedIdx: index('idx_user_read_created').on(table.userId, table.isRead, table.createdAt)
  };
});

// Hashtags Table
export const hashtags = pgTable('hashtags', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull().unique(),
  postsCount: integer('posts_count').default(0),
  createdAt: timestamp('created_at').defaultNow()
}, (table) => {
  return {
    nameIdx: index('idx_name').on(table.name),
    postsCountIdx: index('idx_posts_count').on(table.postsCount)
  };
});

// Post Hashtags Table
export const postHashtags = pgTable('post_hashtags', {
  id: uuid('id').primaryKey().defaultRandom(),
  postId: uuid('post_id').notNull().references(() => posts.id, { onDelete: 'cascade' }),
  hashtagId: uuid('hashtag_id').notNull().references(() => hashtags.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').defaultNow()
}, (table) => {
  return {
    postIdIdx: index('idx_post_id').on(table.postId),
    hashtagIdIdx: index('idx_hashtag_id').on(table.hashtagId),
    uniquePostHashtag: unique('unique_post_hashtag').on(table.postId, table.hashtagId)
  };
});