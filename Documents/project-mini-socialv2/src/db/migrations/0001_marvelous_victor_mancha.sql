DROP INDEX "idx_reports_reporter";--> statement-breakpoint
DROP INDEX "idx_reports_reported_user";--> statement-breakpoint
DROP INDEX "idx_reports_post";--> statement-breakpoint
DROP INDEX "idx_reports_comment";--> statement-breakpoint
DROP INDEX "idx_reports_type";--> statement-breakpoint
CREATE INDEX "idx_notifications_post_id" ON "notifications" USING btree ("post_id");--> statement-breakpoint
CREATE INDEX "idx_notifications_comment_id" ON "notifications" USING btree ("comment_id");--> statement-breakpoint
CREATE INDEX "idx_reports_reporter_id" ON "reports" USING btree ("reporter_id");--> statement-breakpoint
CREATE INDEX "idx_reports_reported_user_id" ON "reports" USING btree ("reported_user_id");--> statement-breakpoint
CREATE INDEX "idx_reports_post_id" ON "reports" USING btree ("post_id");--> statement-breakpoint
CREATE INDEX "idx_reports_comment_id" ON "reports" USING btree ("comment_id");--> statement-breakpoint
CREATE INDEX "idx_reports_report_type" ON "reports" USING btree ("report_type");