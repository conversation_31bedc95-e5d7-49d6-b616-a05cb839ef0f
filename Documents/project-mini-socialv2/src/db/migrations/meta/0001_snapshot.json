{"id": "f6a0b9ce-d143-48ab-a0dd-f1a253d37bfe", "prevId": "5a866a0e-0d6f-4000-a1f4-51cc5e5d8c8a", "version": "7", "dialect": "postgresql", "tables": {"public.comment_likes": {"name": "comment_likes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "comment_id": {"name": "comment_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_comment_likes_comment_id": {"name": "idx_comment_likes_comment_id", "columns": [{"expression": "comment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_comment_likes_user_id": {"name": "idx_comment_likes_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"comment_likes_user_id_users_id_fk": {"name": "comment_likes_user_id_users_id_fk", "tableFrom": "comment_likes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "comment_likes_comment_id_comments_id_fk": {"name": "comment_likes_comment_id_comments_id_fk", "tableFrom": "comment_likes", "tableTo": "comments", "columnsFrom": ["comment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_user_comment_like": {"name": "unique_user_comment_like", "nullsNotDistinct": false, "columns": ["user_id", "comment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.comments": {"name": "comments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "post_id": {"name": "post_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parent_comment_id": {"name": "parent_comment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "likes_count": {"name": "likes_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "replies_count": {"name": "replies_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"idx_comments_post_id": {"name": "idx_comments_post_id", "columns": [{"expression": "post_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_comments_user_id": {"name": "idx_comments_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_comments_parent_comment": {"name": "idx_comments_parent_comment", "columns": [{"expression": "parent_comment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_comments_post_created": {"name": "idx_comments_post_created", "columns": [{"expression": "post_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"comments_user_id_users_id_fk": {"name": "comments_user_id_users_id_fk", "tableFrom": "comments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "comments_post_id_posts_id_fk": {"name": "comments_post_id_posts_id_fk", "tableFrom": "comments", "tableTo": "posts", "columnsFrom": ["post_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "comments_parent_comment_id_comments_id_fk": {"name": "comments_parent_comment_id_comments_id_fk", "tableFrom": "comments", "tableTo": "comments", "columnsFrom": ["parent_comment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.follows": {"name": "follows", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "follower_id": {"name": "follower_id", "type": "uuid", "primaryKey": false, "notNull": true}, "following_id": {"name": "following_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'accepted'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_follows_follower": {"name": "idx_follows_follower", "columns": [{"expression": "follower_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_follows_following": {"name": "idx_follows_following", "columns": [{"expression": "following_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_follows_status": {"name": "idx_follows_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"follows_follower_id_users_id_fk": {"name": "follows_follower_id_users_id_fk", "tableFrom": "follows", "tableTo": "users", "columnsFrom": ["follower_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "follows_following_id_users_id_fk": {"name": "follows_following_id_users_id_fk", "tableFrom": "follows", "tableTo": "users", "columnsFrom": ["following_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_follow": {"name": "unique_follow", "nullsNotDistinct": false, "columns": ["follower_id", "following_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.hashtags": {"name": "hashtags", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "posts_count": {"name": "posts_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_hashtags_name": {"name": "idx_hashtags_name", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_hashtags_posts_count": {"name": "idx_hashtags_posts_count", "columns": [{"expression": "posts_count", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"hashtags_name_unique": {"name": "hashtags_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.likes": {"name": "likes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "post_id": {"name": "post_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_likes_post_id": {"name": "idx_likes_post_id", "columns": [{"expression": "post_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_likes_user_id": {"name": "idx_likes_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"likes_user_id_users_id_fk": {"name": "likes_user_id_users_id_fk", "tableFrom": "likes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "likes_post_id_posts_id_fk": {"name": "likes_post_id_posts_id_fk", "tableFrom": "likes", "tableTo": "posts", "columnsFrom": ["post_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_user_post_like": {"name": "unique_user_post_like", "nullsNotDistinct": false, "columns": ["user_id", "post_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "actor_id": {"name": "actor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "post_id": {"name": "post_id", "type": "uuid", "primaryKey": false, "notNull": false}, "comment_id": {"name": "comment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_notifications_user_id": {"name": "idx_notifications_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_actor_id": {"name": "idx_notifications_actor_id", "columns": [{"expression": "actor_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_post_id": {"name": "idx_notifications_post_id", "columns": [{"expression": "post_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_comment_id": {"name": "idx_notifications_comment_id", "columns": [{"expression": "comment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_is_read": {"name": "idx_notifications_is_read", "columns": [{"expression": "is_read", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_created_at": {"name": "idx_notifications_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_user_read_created": {"name": "idx_notifications_user_read_created", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_read", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notifications_actor_id_users_id_fk": {"name": "notifications_actor_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["actor_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "notifications_post_id_posts_id_fk": {"name": "notifications_post_id_posts_id_fk", "tableFrom": "notifications", "tableTo": "posts", "columnsFrom": ["post_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "notifications_comment_id_comments_id_fk": {"name": "notifications_comment_id_comments_id_fk", "tableFrom": "notifications", "tableTo": "comments", "columnsFrom": ["comment_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.post_hashtags": {"name": "post_hashtags", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "post_id": {"name": "post_id", "type": "uuid", "primaryKey": false, "notNull": true}, "hashtag_id": {"name": "hashtag_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_post_hashtags_post_id": {"name": "idx_post_hashtags_post_id", "columns": [{"expression": "post_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_post_hashtags_hashtag_id": {"name": "idx_post_hashtags_hashtag_id", "columns": [{"expression": "hashtag_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"post_hashtags_post_id_posts_id_fk": {"name": "post_hashtags_post_id_posts_id_fk", "tableFrom": "post_hashtags", "tableTo": "posts", "columnsFrom": ["post_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "post_hashtags_hashtag_id_hashtags_id_fk": {"name": "post_hashtags_hashtag_id_hashtags_id_fk", "tableFrom": "post_hashtags", "tableTo": "hashtags", "columnsFrom": ["hashtag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_post_hashtag": {"name": "unique_post_hashtag", "nullsNotDistinct": false, "columns": ["post_id", "hashtag_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts": {"name": "posts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "media_urls": {"name": "media_urls", "type": "json", "primaryKey": false, "notNull": false}, "likes_count": {"name": "likes_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "comments_count": {"name": "comments_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "shares_count": {"name": "shares_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"idx_posts_user_id": {"name": "idx_posts_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_posts_created_at": {"name": "idx_posts_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_posts_user_created": {"name": "idx_posts_user_created", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_user_id_users_id_fk": {"name": "posts_user_id_users_id_fk", "tableFrom": "posts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reports": {"name": "reports", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "reporter_id": {"name": "reporter_id", "type": "uuid", "primaryKey": false, "notNull": true}, "reported_user_id": {"name": "reported_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "post_id": {"name": "post_id", "type": "uuid", "primaryKey": false, "notNull": false}, "comment_id": {"name": "comment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "report_type": {"name": "report_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "admin_notes": {"name": "admin_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_reports_reporter_id": {"name": "idx_reports_reporter_id", "columns": [{"expression": "reporter_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_reports_reported_user_id": {"name": "idx_reports_reported_user_id", "columns": [{"expression": "reported_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_reports_post_id": {"name": "idx_reports_post_id", "columns": [{"expression": "post_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_reports_comment_id": {"name": "idx_reports_comment_id", "columns": [{"expression": "comment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_reports_status": {"name": "idx_reports_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_reports_report_type": {"name": "idx_reports_report_type", "columns": [{"expression": "report_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"reports_reporter_id_users_id_fk": {"name": "reports_reporter_id_users_id_fk", "tableFrom": "reports", "tableTo": "users", "columnsFrom": ["reporter_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reports_reported_user_id_users_id_fk": {"name": "reports_reported_user_id_users_id_fk", "tableFrom": "reports", "tableTo": "users", "columnsFrom": ["reported_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "reports_post_id_posts_id_fk": {"name": "reports_post_id_posts_id_fk", "tableFrom": "reports", "tableTo": "posts", "columnsFrom": ["post_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "reports_comment_id_comments_id_fk": {"name": "reports_comment_id_comments_id_fk", "tableFrom": "reports", "tableTo": "comments", "columnsFrom": ["comment_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'user'"}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "profile_image_url": {"name": "profile_image_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "cover_image_url": {"name": "cover_image_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "followers_count": {"name": "followers_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "following_count": {"name": "following_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "posts_count": {"name": "posts_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"idx_users_username": {"name": "idx_users_username", "columns": [{"expression": "username", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_users_email": {"name": "idx_users_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_users_created_at": {"name": "idx_users_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}