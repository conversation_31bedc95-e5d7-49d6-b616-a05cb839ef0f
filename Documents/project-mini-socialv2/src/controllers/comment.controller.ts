import { Request, Response, NextFunction } from 'express';
import { commentService } from '../services/comment.service';

export const commentController = {
  /**
   * Create a new comment
   */
  async createComment(req: Request, res: Response, next: NextFunction) {
    try {
      const { id: postId } = req.params;
      const userId = req.user!.id;
      const { content, parentCommentId } = req.body;
      
      const comment = await commentService.create(postId, userId, {
        content,
        parentCommentId
      });
      
      res.status(201).json({
        status: 'success',
        data: comment
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Get comments for a post
   */
  async getComments(req: Request, res: Response, next: NextFunction) {
    try {
      const { id: postId } = req.params;
      const page = Number(req.query.page) || 1;
      const limit = Number(req.query.limit) || 20;
      const currentUserId = req.user?.id;
      
      const comments = await commentService.getPostComments(
        postId,
        page,
        limit,
        currentUserId
      );
      
      res.status(200).json({
        status: 'success',
        data: comments,
        meta: {
          page,
          limit
        }
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Update a comment
   */
  async updateComment(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      const { content } = req.body;
      
      const updatedComment = await commentService.update(
        id,
        { content },
        userId
      );
      
      res.status(200).json({
        status: 'success',
        data: updatedComment
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Delete a comment
   */
  async deleteComment(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const result = await commentService.delete(id, userId);
      
      res.status(200).json({
        status: 'success',
        data: result
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Like a comment
   */
  async likeComment(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const result = await commentService.likeComment(id, userId);
      
      res.status(200).json({
        status: 'success',
        data: result
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Unlike a comment
   */
  async unlikeComment(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const result = await commentService.unlikeComment(id, userId);
      
      res.status(200).json({
        status: 'success',
        data: result
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Get comment replies
   */
  async getCommentReplies(req: Request, res: Response, next: NextFunction) {
    try {
      const { id: commentId } = req.params;
      const page = Number(req.query.page) || 1;
      const limit = Number(req.query.limit) || 20;
      const currentUserId = req.user?.id;
      
      const replies = await commentService.getCommentReplies(
        commentId,
        page,
        limit,
        currentUserId
      );
      
      res.status(200).json({
        status: 'success',
        data: replies,
        meta: {
          page,
          limit
        }
      });
    } catch (error) {
      next(error);
    }
  }
};