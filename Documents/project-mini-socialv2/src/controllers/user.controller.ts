import { Request, Response, NextFunction } from 'express';
import { userService } from '../services/user.service';

export const userController = {
  /**
   * Get user profile
   */
  async getUser(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const currentUserId = req.user?.id;
      
      const user = await userService.findById(id, currentUserId);
      
      res.status(200).json({
        status: 'success',
        data: user
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Update user profile
   */
  async updateUser(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const currentUserId = req.user!.id;
      
      const updatedUser = await userService.update(id, req.body, currentUserId);
      
      res.status(200).json({
        status: 'success',
        data: updatedUser
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Follow a user
   */
  async followUser(req: Request, res: Response, next: NextFunction) {
    try {
      const { id: followingId } = req.params;
      const followerId = req.user!.id;
      
      const result = await userService.followUser(followingId, followerId);
      
      res.status(200).json({
        status: 'success',
        data: result
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Unfollow a user
   */
  async unfollowUser(req: Request, res: Response, next: NextFunction) {
    try {
      const { id: followingId } = req.params;
      const followerId = req.user!.id;
      
      const result = await userService.unfollowUser(followingId, followerId);
      
      res.status(200).json({
        status: 'success',
        data: result
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Search users
   */
  async searchUsers(req: Request, res: Response, next: NextFunction) {
    try {
      const { query } = req.query;
      const page = Number(req.query.page) || 1;
      const limit = Number(req.query.limit) || 20;
      
      if (!query || typeof query !== 'string') {
        return res.status(200).json({
          status: 'success',
          data: []
        });
      }
      
      const users = await userService.searchUsers(query, page, limit);

      return res.status(200).json({
        status: 'success',
        data: users
      });
    } catch (error) {
      next(error);
    }
  }
};