import { Request, Response, NextFunction } from 'express';
import { postService } from '../services/post.service';

export const postController = {
  /**
   * Create a new post
   */
  async createPost(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user!.id;
      const { content, mediaUrls, isPublic } = req.body;
      
      const post = await postService.create(userId, {
        content,
        mediaUrls,
        isPublic
      });
      
      res.status(201).json({
        status: 'success',
        data: post
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Get a post by ID
   */
  async getPost(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const currentUserId = req.user?.id;
      
      const post = await postService.findById(id, currentUserId);
      
      res.status(200).json({
        status: 'success',
        data: post
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Update a post
   */
  async updatePost(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      const { content, mediaUrls, isPublic } = req.body;
      
      const updatedPost = await postService.update(
        id,
        {
          content,
          mediaUrls,
          isPublic
        },
        userId
      );
      
      res.status(200).json({
        status: 'success',
        data: updatedPost
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Delete a post
   */
  async deletePost(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const result = await postService.delete(id, userId);
      
      res.status(200).json({
        status: 'success',
        data: result
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Like a post
   */
  async likePost(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const result = await postService.likePost(id, userId);
      
      res.status(200).json({
        status: 'success',
        data: result
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Unlike a post
   */
  async unlikePost(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const result = await postService.unlikePost(id, userId);
      
      res.status(200).json({
        status: 'success',
        data: result
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * List posts with various filters
   */
  async listPosts(req: Request, res: Response, next: NextFunction) {
    try {
      const page = Number(req.query.page) || 1;
      const limit = Number(req.query.limit) || 20;
      const userId = req.query.userId as string | undefined;
      const currentUserId = req.user?.id;
      
      const posts = await postService.listPosts({
        page,
        limit,
        userId: userId || null,
        currentUserId: currentUserId || null
      });
      
      res.status(200).json({
        status: 'success',
        data: posts,
        meta: {
          page,
          limit
        }
      });
    } catch (error) {
      next(error);
    }
  }
};