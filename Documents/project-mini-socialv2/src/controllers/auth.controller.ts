import { Request, Response, NextFunction } from 'express';
import { authService } from '../services/auth.service';

export const authController = {
  /**
   * Register a new user
   */
  async register(req: Request, res: Response, next: NextFunction) {
    try {
      const { username, email, password, displayName, bio } = req.body;
      
      const user = await authService.register({
        username,
        email,
        password,
        displayName,
        bio
      });
      
      res.status(201).json({
        status: 'success',
        data: user
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Login a user
   */
  async login(req: Request, res: Response, next: NextFunction) {
    try {
      const { email, password } = req.body;
      
      const tokens = await authService.login({ email, password });
      
      res.status(200).json({
        status: 'success',
        data: tokens
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Refresh access token
   */
  async refreshToken(req: Request, res: Response, next: NextFunction) {
    try {
      const { refreshToken } = req.body;
      
      const tokens = await authService.refreshToken(refreshToken);
      
      res.status(200).json({
        status: 'success',
        data: tokens
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Get current user
   */
  async getCurrentUser(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user!.id;
      
      const user = await authService.getCurrentUser(userId);
      
      res.status(200).json({
        status: 'success',
        data: user
      });
    } catch (error) {
      next(error);
    }
  }
};