import { Request, Response, NextFunction } from 'express';
import { feedService } from '../services/feed.service';

export const feedController = {
  /**
   * Get user feed
   */
  async getFeed(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user!.id;
      const page = Number(req.query.page) || 1;
      const limit = Number(req.query.limit) || 20;
      
      const posts = await feedService.getUserFeed(userId, page, limit);
      
      res.status(200).json({
        status: 'success',
        data: posts,
        meta: {
          page,
          limit
        }
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Get explore feed
   */
  async getExploreFeed(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user!.id;
      const page = Number(req.query.page) || 1;
      const limit = Number(req.query.limit) || 20;
      
      const posts = await feedService.getExploreFeed(userId, page, limit);
      
      res.status(200).json({
        status: 'success',
        data: posts,
        meta: {
          page,
          limit
        }
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Get trending hashtags
   */
  async getTrendingHashtags(req: Request, res: Response, next: NextFunction) {
    try {
      const limit = Number(req.query.limit) || 10;
      
      const hashtags = await feedService.getTrendingHashtags(limit);
      
      res.status(200).json({
        status: 'success',
        data: hashtags
      });
    } catch (error) {
      next(error);
    }
  },
  
  /**
   * Get posts by hashtag
   */
  async getHashtagPosts(req: Request, res: Response, next: NextFunction) {
    try {
      const { tag } = req.params;
      const page = Number(req.query.page) || 1;
      const limit = Number(req.query.limit) || 20;
      const currentUserId = req.user?.id;
      
      const posts = await feedService.getPostsByHashtag(tag, page, limit, currentUserId);
      
      res.status(200).json({
        status: 'success',
        data: posts,
        meta: {
          page,
          limit,
          tag
        }
      });
    } catch (error) {
      next(error);
    }
  }
};