import { z } from 'zod';

export const updateUserSchema = z.object({
  body: z.object({
    username: z
      .string()
      .min(3, 'Username must be at least 3 characters')
      .max(50, 'Username cannot exceed 50 characters')
      .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')
      .optional(),
    email: z
      .string()
      .email('Invalid email format')
      .optional(),
    displayName: z
      .string()
      .max(100, 'Display name cannot exceed 100 characters')
      .optional(),
    bio: z
      .string()
      .max(500, 'Bio cannot exceed 500 characters')
      .optional(),
    profileImageUrl: z
      .string()
      .url('Profile image URL must be a valid URL')
      .optional()
      .nullable(),
    coverImageUrl: z
      .string()
      .url('Cover image URL must be a valid URL')
      .optional()
      .nullable(),
    isPrivate: z
      .boolean()
      .optional()
  })
});

export const userIdSchema = z.object({
  params: z.object({
    id: z.string().uuid('User ID must be a valid UUID')
  })
});

export const followUserSchema = z.object({
  params: z.object({
    id: z.string().uuid('User ID must be a valid UUID')
  })
});