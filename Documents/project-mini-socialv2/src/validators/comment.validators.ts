import { z } from 'zod';

export const createCommentSchema = z.object({
  params: z.object({
    id: z.string().uuid('Post ID must be a valid UUID')
  }),
  body: z.object({
    content: z
      .string()
      .min(1, 'Content is required')
      .max(1000, 'Content cannot exceed 1000 characters'),
    parentCommentId: z
      .string()
      .uuid('Parent comment ID must be a valid UUID')
      .optional()
  })
});

export const getCommentsSchema = z.object({
  params: z.object({
    id: z.string().uuid('Post ID must be a valid UUID')
  }),
  query: z.object({
    page: z
      .string()
      .regex(/^\d+$/, 'Page must be a number')
      .optional()
      .transform(val => val ? parseInt(val) : 1),
    limit: z
      .string()
      .regex(/^\d+$/, 'Limit must be a number')
      .optional()
      .transform(val => val ? Math.min(parseInt(val), 50) : 20)
  })
});

export const updateCommentSchema = z.object({
  params: z.object({
    id: z.string().uuid('Comment ID must be a valid UUID')
  }),
  body: z.object({
    content: z
      .string()
      .min(1, 'Content is required')
      .max(1000, 'Content cannot exceed 1000 characters')
  })
});

export const deleteCommentSchema = z.object({
  params: z.object({
    id: z.string().uuid('Comment ID must be a valid UUID')
  })
});