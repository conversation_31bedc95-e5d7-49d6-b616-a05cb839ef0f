import { z } from 'zod';

export const createPostSchema = z.object({
  body: z.object({
    content: z
      .string()
      .min(1, 'Content is required')
      .max(2000, 'Content cannot exceed 2000 characters'),
    mediaUrls: z
      .array(z.string().url('Media URL must be a valid URL'))
      .optional(),
    isPublic: z
      .boolean()
      .optional()
  })
});

export const updatePostSchema = z.object({
  params: z.object({
    id: z.string().uuid('Post ID must be a valid UUID')
  }),
  body: z.object({
    content: z
      .string()
      .min(1, 'Content is required')
      .max(2000, 'Content cannot exceed 2000 characters')
      .optional(),
    mediaUrls: z
      .array(z.string().url('Media URL must be a valid URL'))
      .optional(),
    isPublic: z
      .boolean()
      .optional()
  })
});

export const getPostSchema = z.object({
  params: z.object({
    id: z.string().uuid('Post ID must be a valid UUID')
  })
});

export const listPostsSchema = z.object({
  query: z.object({
    page: z
      .string()
      .regex(/^\d+$/, 'Page must be a number')
      .optional()
      .transform(val => val ? parseInt(val) : 1),
    limit: z
      .string()
      .regex(/^\d+$/, 'Limit must be a number')
      .optional()
      .transform(val => val ? Math.min(parseInt(val), 50) : 20),
    userId: z
      .string()
      .uuid('User ID must be a valid UUID')
      .optional()
  })
});