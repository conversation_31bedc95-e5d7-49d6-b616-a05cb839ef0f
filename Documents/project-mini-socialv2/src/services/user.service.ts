import { v4 as uuidv4 } from 'uuid';
import { eq, and, or, not } from 'drizzle-orm';
import { db } from '../db';
import { users, follows, posts } from '../db/schema';
import { NotFoundError, BadRequestError, ForbiddenError } from '../utils/errors';

// Types
type UserUpdateData = {
  username?: string;
  email?: string;
  displayName?: string;
  bio?: string;
  profileImageUrl?: string | null;
  coverImageUrl?: string | null;
  isPrivate?: boolean;
};

// User Service Implementation
export const userService = {
  /**
   * Find user by ID
   */
  async findById(id: string, currentUserId?: string) {
    const userResults = await db.select({
      id: users.id,
      username: users.username,
      displayName: users.displayName,
      bio: users.bio,
      profileImageUrl: users.profileImageUrl,
      coverImageUrl: users.coverImageUrl,
      isVerified: users.isVerified,
      isPrivate: users.isPrivate,
      followersCount: users.followersCount,
      followingCount: users.followingCount,
      postsCount: users.postsCount,
      createdAt: users.createdAt
    })
    .from(users)
    .where(eq(users.id, id))
    .limit(1);
    
    if (userResults.length === 0) {
      throw new NotFoundError('User not found');
    }
    
    const user = userResults[0];
    
    // Add follow status if currentUserId is provided
    let isFollowing = false;
    let isFollowedBy = false;
    
    if (currentUserId) {
      // Check if current user follows the requested user
      const followResults = await db
        .select()
        .from(follows)
        .where(
          and(
            eq(follows.followerId, currentUserId),
            eq(follows.followingId, id),
            eq(follows.status, 'accepted')
          )
        )
        .limit(1);
      
      isFollowing = followResults.length > 0;
      
      // Check if requested user follows the current user
      const followedByResults = await db
        .select()
        .from(follows)
        .where(
          and(
            eq(follows.followerId, id),
            eq(follows.followingId, currentUserId),
            eq(follows.status, 'accepted')
          )
        )
        .limit(1);
      
      isFollowedBy = followedByResults.length > 0;
    }
    
    return {
      ...user,
      isFollowing,
      isFollowedBy
    };
  },
  
  /**
   * Update user profile
   */
  async update(id: string, updateData: UserUpdateData, currentUserId: string) {
    // Verify user exists
    const userResults = await db.select().from(users).where(eq(users.id, id)).limit(1);
    
    if (userResults.length === 0) {
      throw new NotFoundError('User not found');
    }
    
    // Check if current user has permission to update this user
    if (id !== currentUserId) {
      throw new ForbiddenError('You do not have permission to update this user');
    }
    
    // Check if username is already taken
    if (updateData.username) {
      const existingUserResults = await db
        .select()
        .from(users)
        .where(and(eq(users.username, updateData.username), not(eq(users.id, id))))
        .limit(1);
      
      if (existingUserResults.length > 0) {
        throw new BadRequestError('Username already taken');
      }
    }
    
    // Check if email is already in use
    if (updateData.email) {
      const existingUserResults = await db
        .select()
        .from(users)
        .where(and(eq(users.email, updateData.email), not(eq(users.id, id))))
        .limit(1);
      
      if (existingUserResults.length > 0) {
        throw new BadRequestError('Email already in use');
      }
    }
    
    // Update the user
    await db.update(users)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(users.id, id));
    
    // Fetch the updated user
    return this.findById(id);
  },
  
  /**
   * Follow a user
   */
  async followUser(followingId: string, followerId: string) {
    // Verify both users exist
    const followingUserResults = await db.select().from(users).where(eq(users.id, followingId)).limit(1);
    
    if (followingUserResults.length === 0) {
      throw new NotFoundError('User to follow not found');
    }
    
    // Check if already following
    const existingFollowResults = await db
      .select()
      .from(follows)
      .where(
        and(
          eq(follows.followerId, followerId),
          eq(follows.followingId, followingId)
        )
      )
      .limit(1);
    
    if (existingFollowResults.length > 0) {
      throw new BadRequestError('You are already following this user');
    }
    
    const followingUser = followingUserResults[0];
    
    // Determine if follow needs approval
    const status = followingUser.isPrivate ? 'pending' : 'accepted';
    
    // Create follow record
    const follow = {
      id: uuidv4(),
      followerId,
      followingId,
      status,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await db.insert(follows).values(follow);
    
    // Update counts if accepted immediately
    if (status === 'accepted') {
      await db.update(users)
        .set({ followersCount: users.followersCount + 1 })
        .where(eq(users.id, followingId));
      
      await db.update(users)
        .set({ followingCount: users.followingCount + 1 })
        .where(eq(users.id, followerId));
    }
    
    return { status };
  },
  
  /**
   * Unfollow a user
   */
  async unfollowUser(followingId: string, followerId: string) {
    // Verify the follow relationship exists
    const followResults = await db
      .select()
      .from(follows)
      .where(
        and(
          eq(follows.followerId, followerId),
          eq(follows.followingId, followingId)
        )
      )
      .limit(1);
    
    if (followResults.length === 0) {
      throw new NotFoundError('Follow relationship not found');
    }
    
    const follow = followResults[0];
    
    // Delete the follow record
    await db
      .delete(follows)
      .where(
        and(
          eq(follows.followerId, followerId),
          eq(follows.followingId, followingId)
        )
      );
    
    // Update counts if the follow was accepted
    if (follow.status === 'accepted') {
      await db.update(users)
        .set({ followersCount: users.followersCount - 1 })
        .where(eq(users.id, followingId));
      
      await db.update(users)
        .set({ followingCount: users.followingCount - 1 })
        .where(eq(users.id, followerId));
    }
    
    return { success: true };
  },
  
  /**
   * Search users by username or display name
   */
  async searchUsers(query: string, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const results = await db
      .select({
        id: users.id,
        username: users.username,
        displayName: users.displayName,
        profileImageUrl: users.profileImageUrl,
        isVerified: users.isVerified
      })
      .from(users)
      .where(
        or(
          eq(users.username, query),
          eq(users.displayName, query)
        )
      )
      .limit(limit)
      .offset(offset);
    
    return results;
  }
};