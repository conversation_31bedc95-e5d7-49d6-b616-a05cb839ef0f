import { v4 as uuidv4 } from 'uuid';
import { eq, and, desc, isNull, sql, inArray } from 'drizzle-orm';
import { db } from '../db';
import { comments, posts, users, commentLikes } from '../db/schema';
import { NotFoundError, ForbiddenError } from '../utils/errors';

// Types
type CreateCommentData = {
  content: string;
  parentCommentId?: string;
};

type UpdateCommentData = {
  content: string;
};

// Comment Service Implementation
export const commentService = {
  /**
   * Create a new comment
   */
  async create(postId: string, userId: string, commentData: CreateCommentData) {
    // Verify post exists
    const postResults = await db.select().from(posts).where(eq(posts.id, postId)).limit(1);
    
    if (postResults.length === 0) {
      throw new NotFoundError('Post not found');
    }
    
    // Verify parent comment if provided
    if (commentData.parentCommentId) {
      const parentCommentResults = await db
        .select()
        .from(comments)
        .where(
          and(
            eq(comments.id, commentData.parentCommentId),
            eq(comments.postId, postId)
          )
        )
        .limit(1);
      
      if (parentCommentResults.length === 0) {
        throw new NotFoundError('Parent comment not found');
      }
    }
    
    // Create comment
    const newComment = {
      id: uuidv4(),
      postId,
      userId,
      parentCommentId: commentData.parentCommentId || null,
      content: commentData.content,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await db.insert(comments).values(newComment);
    
    // Update post comments count
    await db.update(posts)
      .set({ commentsCount: sql`${posts.commentsCount} + 1` })
      .where(eq(posts.id, postId));

    // If this is a reply, update parent comment replies count
    if (commentData.parentCommentId) {
      await db.update(comments)
        .set({ repliesCount: sql`${comments.repliesCount} + 1` })
        .where(eq(comments.id, commentData.parentCommentId));
    }
    
    // Return created comment
    return this.findById(newComment.id, userId);
  },
  
  /**
   * Find comment by ID
   */
  async findById(id: string, currentUserId?: string) {
    const commentResults = await db
      .select({
        id: comments.id,
        content: comments.content,
        postId: comments.postId,
        parentCommentId: comments.parentCommentId,
        likesCount: comments.likesCount,
        repliesCount: comments.repliesCount,
        createdAt: comments.createdAt,
        updatedAt: comments.updatedAt,
        userId: comments.userId,
        username: users.username,
        displayName: users.displayName,
        profileImageUrl: users.profileImageUrl,
        isVerified: users.isVerified
      })
      .from(comments)
      .innerJoin(users, eq(comments.userId, users.id))
      .where(eq(comments.id, id))
      .limit(1);
    
    if (commentResults.length === 0) {
      throw new NotFoundError('Comment not found');
    }
    
    const comment = commentResults[0];
    
    // Check if current user liked the comment
    let isLiked = false;
    
    if (currentUserId) {
      const likeResults = await db
        .select()
        .from(commentLikes)
        .where(
          and(
            eq(commentLikes.commentId, id),
            eq(commentLikes.userId, currentUserId)
          )
        )
        .limit(1);
      
      isLiked = likeResults.length > 0;
    }
    
    return {
      ...comment,
      isLiked
    };
  },
  
  /**
   * Update a comment
   */
  async update(id: string, updateData: UpdateCommentData, currentUserId: string) {
    // Verify comment exists and belongs to user
    const commentResults = await db
      .select()
      .from(comments)
      .where(eq(comments.id, id))
      .limit(1);
    
    if (commentResults.length === 0) {
      throw new NotFoundError('Comment not found');
    }
    
    const comment = commentResults[0];
    
    if (comment.userId !== currentUserId) {
      throw new ForbiddenError('You do not have permission to update this comment');
    }
    
    // Update the comment
    await db.update(comments)
      .set({
        content: updateData.content,
        updatedAt: new Date()
      })
      .where(eq(comments.id, id));
    
    // Return the updated comment
    return this.findById(id, currentUserId);
  },
  
  /**
   * Delete a comment
   */
  async delete(id: string, currentUserId: string) {
    // Verify comment exists and belongs to user
    const commentResults = await db
      .select()
      .from(comments)
      .where(eq(comments.id, id))
      .limit(1);
    
    if (commentResults.length === 0) {
      throw new NotFoundError('Comment not found');
    }
    
    const comment = commentResults[0];
    
    if (comment.userId !== currentUserId) {
      throw new ForbiddenError('You do not have permission to delete this comment');
    }
    
    // Soft delete the comment
    await db.update(comments)
      .set({
        content: '[Deleted]',
        deletedAt: new Date()
      })
      .where(eq(comments.id, id));
    
    // Update post comments count
    await db.update(posts)
      .set({ commentsCount: sql`${posts.commentsCount} - 1` })
      .where(eq(posts.id, comment.postId));

    // If this is a reply, update parent comment replies count
    if (comment.parentCommentId) {
      await db.update(comments)
        .set({ repliesCount: sql`${comments.repliesCount} - 1` })
        .where(eq(comments.id, comment.parentCommentId));
    }
    
    return { success: true };
  },
  
  /**
   * Get comments for a post
   */
  async getPostComments(postId: string, page = 1, limit = 20, currentUserId?: string) {
    const offset = (page - 1) * limit;
    
    // First, get top-level comments
    const results = await db
      .select({
        id: comments.id,
        content: comments.content,
        postId: comments.postId,
        parentCommentId: comments.parentCommentId,
        likesCount: comments.likesCount,
        repliesCount: comments.repliesCount,
        createdAt: comments.createdAt,
        updatedAt: comments.updatedAt,
        userId: comments.userId,
        username: users.username,
        displayName: users.displayName,
        profileImageUrl: users.profileImageUrl,
        isVerified: users.isVerified
      })
      .from(comments)
      .innerJoin(users, eq(comments.userId, users.id))
      .where(
        and(
          eq(comments.postId, postId),
          isNull(comments.parentCommentId),
          isNull(comments.deletedAt)
        )
      )
      .orderBy(desc(comments.createdAt))
      .limit(limit)
      .offset(offset);
    
    // Add isLiked field if currentUserId is provided
    let enhancedResults = results;
    
    if (currentUserId && results.length > 0) {
      const commentIds = results.map(comment => comment.id);
      
      // Get all likes for these comments from the current user
      const userLikes = await db
        .select()
        .from(commentLikes)
        .where(
          and(
            eq(commentLikes.userId, currentUserId),
            inArray(commentLikes.commentId, commentIds)
          )
        );
      
      // Create a map for faster lookups
      const likedCommentMap = new Map(userLikes.map(like => [like.commentId, true]));
      
      enhancedResults = results.map(comment => ({
        ...comment,
        isLiked: likedCommentMap.has(comment.id)
      }));
    }
    
    return enhancedResults;
  },
  
  /**
   * Like a comment
   */
  async likeComment(commentId: string, userId: string) {
    // Verify comment exists
    const commentResults = await db.select().from(comments).where(eq(comments.id, commentId)).limit(1);
    
    if (commentResults.length === 0) {
      throw new NotFoundError('Comment not found');
    }
    
    // Check if already liked
    const existingLikeResults = await db
      .select()
      .from(commentLikes)
      .where(
        and(
          eq(commentLikes.commentId, commentId),
          eq(commentLikes.userId, userId)
        )
      )
      .limit(1);
    
    if (existingLikeResults.length > 0) {
      return { already_liked: true };
    }
    
    // Create like
    await db.insert(commentLikes).values({
      id: uuidv4(),
      commentId,
      userId,
      createdAt: new Date()
    });
    
    // Update comment likes count
    await db.update(comments)
      .set({ likesCount: sql`${comments.likesCount} + 1` })
      .where(eq(comments.id, commentId));
    
    return { success: true };
  },
  
  /**
   * Unlike a comment
   */
  async unlikeComment(commentId: string, userId: string) {
    // Verify like exists
    const likeResults = await db
      .select()
      .from(commentLikes)
      .where(
        and(
          eq(commentLikes.commentId, commentId),
          eq(commentLikes.userId, userId)
        )
      )
      .limit(1);
    
    if (likeResults.length === 0) {
      throw new NotFoundError('Like not found');
    }
    
    // Remove like
    await db
      .delete(commentLikes)
      .where(
        and(
          eq(commentLikes.commentId, commentId),
          eq(commentLikes.userId, userId)
        )
      );
    
    // Update comment likes count
    await db.update(comments)
      .set({ likesCount: sql`${comments.likesCount} - 1` })
      .where(eq(comments.id, commentId));
    
    return { success: true };
  },
  
  /**
   * Get replies for a comment
   */
  async getCommentReplies(commentId: string, page = 1, limit = 20, currentUserId?: string) {
    const offset = (page - 1) * limit;
    
    // Get replies
    const results = await db
      .select({
        id: comments.id,
        content: comments.content,
        postId: comments.postId,
        parentCommentId: comments.parentCommentId,
        likesCount: comments.likesCount,
        repliesCount: comments.repliesCount,
        createdAt: comments.createdAt,
        updatedAt: comments.updatedAt,
        userId: comments.userId,
        username: users.username,
        displayName: users.displayName,
        profileImageUrl: users.profileImageUrl,
        isVerified: users.isVerified
      })
      .from(comments)
      .innerJoin(users, eq(comments.userId, users.id))
      .where(
        and(
          eq(comments.parentCommentId, commentId),
          isNull(comments.deletedAt)
        )
      )
      .orderBy(desc(comments.createdAt))
      .limit(limit)
      .offset(offset);
    
    // Add isLiked field if currentUserId is provided
    let enhancedResults = results;
    
    if (currentUserId && results.length > 0) {
      const commentIds = results.map(comment => comment.id);
      
      // Get all likes for these comments from the current user
      const userLikes = await db
        .select()
        .from(commentLikes)
        .where(
          and(
            eq(commentLikes.userId, currentUserId),
            inArray(commentLikes.commentId, commentIds)
          )
        );
      
      // Create a map for faster lookups
      const likedCommentMap = new Map(userLikes.map(like => [like.commentId, true]));
      
      enhancedResults = results.map(comment => ({
        ...comment,
        isLiked: likedCommentMap.has(comment.id)
      }));
    }
    
    return enhancedResults;
  }
};