import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { db } from '../db';
import { users } from '../db/schema';
import { eq } from 'drizzle-orm';
import { UnauthorizedError, NotFoundError, BadRequestError } from '../utils/errors';

// Types
type UserRegistration = {
  username: string;
  email: string;
  password: string;
  displayName?: string;
  bio?: string;
};

type UserLogin = {
  email: string;
  password: string;
};

type TokenPayload = {
  id: string;
  role?: string;
};

type AuthTokens = {
  accessToken: string;
  refreshToken: string;
};

// Auth Service Implementation
export const authService = {
  /**
   * Register a new user
   */
  async register(userData: UserRegistration): Promise<{id: string, username: string, email: string}> {
    // Check if user already exists
    const existingUserByEmail = await db.select().from(users).where(eq(users.email, userData.email)).limit(1);
    if (existingUserByEmail.length > 0) {
      throw new BadRequestError('Email already in use');
    }
    
    const existingUserByUsername = await db.select().from(users).where(eq(users.username, userData.username)).limit(1);
    if (existingUserByUsername.length > 0) {
      throw new BadRequestError('Username already taken');
    }
    
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash(userData.password, salt);
    
    // Create user
    const newUser = {
      id: uuidv4(),
      username: userData.username,
      email: userData.email,
      passwordHash,
      displayName: userData.displayName || userData.username,
      bio: userData.bio || null,
      role: 'user',
      isVerified: false,
      isPrivate: false,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await db.insert(users).values(newUser);
    
    return {
      id: newUser.id,
      username: newUser.username,
      email: newUser.email
    };
  },
  
  /**
   * Login a user
   */
  async login(credentials: UserLogin): Promise<AuthTokens> {
    // Find user by email
    const userResults = await db.select().from(users).where(eq(users.email, credentials.email)).limit(1);
    
    if (userResults.length === 0) {
      throw new UnauthorizedError('Invalid email or password');
    }
    
    const user = userResults[0];
    
    // Check if account is active
    if (!user.isActive) {
      throw new UnauthorizedError('Account is inactive');
    }
    
    // Check password
    const isPasswordValid = await bcrypt.compare(credentials.password, user.passwordHash);
    
    if (!isPasswordValid) {
      throw new UnauthorizedError('Invalid email or password');
    }
    
    // Generate tokens
    const tokens = this.generateTokens({
      id: user.id,
      role: user.role || undefined
    });
    
    return tokens;
  },
  
  /**
   * Generate JWT tokens for authentication
   */
  generateTokens(payload: TokenPayload): AuthTokens {
    const accessToken = jwt.sign(
      payload,
      process.env.JWT_ACCESS_SECRET as string,
      { expiresIn: process.env.JWT_ACCESS_EXPIRY || '15m' }
    );
    
    const refreshToken = jwt.sign(
      payload,
      process.env.JWT_REFRESH_SECRET as string,
      { expiresIn: process.env.JWT_REFRESH_EXPIRY || '7d' }
    );
    
    return {
      accessToken,
      refreshToken
    };
  },
  
  /**
   * Refresh access token using refresh token
   */
  async refreshToken(token: string): Promise<AuthTokens> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(token, process.env.JWT_REFRESH_SECRET as string) as TokenPayload;
      
      // Find user
      const userResults = await db.select().from(users).where(eq(users.id, decoded.id)).limit(1);
      
      if (userResults.length === 0) {
        throw new UnauthorizedError('User not found');
      }
      
      const user = userResults[0];
      
      // Generate new tokens
      return this.generateTokens({
        id: user.id,
        role: user.role || undefined
      });
    } catch (error) {
      throw new UnauthorizedError('Invalid refresh token');
    }
  },
  
  /**
   * Get current user information
   */
  async getCurrentUser(userId: string) {
    const userResults = await db.select({
      id: users.id,
      username: users.username,
      email: users.email,
      role: users.role,
      displayName: users.displayName,
      bio: users.bio,
      profileImageUrl: users.profileImageUrl,
      coverImageUrl: users.coverImageUrl,
      isVerified: users.isVerified,
      isPrivate: users.isPrivate,
      followersCount: users.followersCount,
      followingCount: users.followingCount,
      postsCount: users.postsCount,
      createdAt: users.createdAt
    })
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);
    
    if (userResults.length === 0) {
      throw new NotFoundError('User not found');
    }
    
    return userResults[0];
  }
};