import { v4 as uuidv4 } from 'uuid';
import { eq, and, desc, sql, not, isNull } from 'drizzle-orm';
import { db } from '../db';
import { posts, users, likes, follows, postHashtags, hashtags } from '../db/schema';
import { NotFoundError, ForbiddenError } from '../utils/errors';

// Types
type CreatePostData = {
  content: string;
  mediaUrls?: string[];
  isPublic?: boolean;
};

type UpdatePostData = {
  content?: string;
  mediaUrls?: string[];
  isPublic?: boolean;
};

// Post Service Implementation
export const postService = {
  /**
   * Create a new post
   */
  async create(userId: string, postData: CreatePostData) {
    // Extract hashtags from content
    const hashtagRegex = /#(\w+)/g;
    const extractedHashtags = [...postData.content.matchAll(hashtagRegex)].map(match => match[1]);
    
    // Create post
    const newPost = {
      id: uuidv4(),
      userId,
      content: postData.content,
      mediaUrls: postData.mediaUrls ? postData.mediaUrls : null,
      isPublic: postData.isPublic !== undefined ? postData.isPublic : true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await db.insert(posts).values(newPost);
    
    // Update user posts count
    await db.update(users)
      .set({ postsCount: sql`${users.postsCount} + 1` })
      .where(eq(users.id, userId));
    
    // Process hashtags if any
    if (extractedHashtags.length > 0) {
      for (const tag of extractedHashtags) {
        // Find or create hashtag
        const hashtagResults = await db.select().from(hashtags).where(eq(hashtags.name, tag)).limit(1);
        
        let hashtagId;
        
        if (hashtagResults.length === 0) {
          // Create new hashtag
          const newHashtag = {
            id: uuidv4(),
            name: tag,
            postsCount: 1,
            createdAt: new Date()
          };
          
          await db.insert(hashtags).values(newHashtag);
          hashtagId = newHashtag.id;
        } else {
          // Update existing hashtag
          hashtagId = hashtagResults[0].id;
          await db.update(hashtags)
            .set({ postsCount: sql`${hashtags.postsCount} + 1` })
            .where(eq(hashtags.id, hashtagId));
        }
        
        // Create post-hashtag relationship
        await db.insert(postHashtags).values({
          id: uuidv4(),
          postId: newPost.id,
          hashtagId,
          createdAt: new Date()
        });
      }
    }
    
    return this.findById(newPost.id, userId);
  },
  
  /**
   * Find post by ID
   */
  async findById(id: string, currentUserId?: string) {
    const postResults = await db
      .select({
        id: posts.id,
        content: posts.content,
        mediaUrls: posts.mediaUrls,
        likesCount: posts.likesCount,
        commentsCount: posts.commentsCount,
        sharesCount: posts.sharesCount,
        isPublic: posts.isPublic,
        createdAt: posts.createdAt,
        updatedAt: posts.updatedAt,
        userId: posts.userId,
        username: users.username,
        displayName: users.displayName,
        profileImageUrl: users.profileImageUrl,
        isVerified: users.isVerified
      })
      .from(posts)
      .innerJoin(users, eq(posts.userId, users.id))
      .where(eq(posts.id, id))
      .limit(1);
    
    if (postResults.length === 0) {
      throw new NotFoundError('Post not found');
    }
    
    const post = postResults[0];
    
    // Check if post is private and user is authorized to view it
    if (!post.isPublic && post.userId !== currentUserId) {
      // Check if current user follows post creator
      if (currentUserId) {
        const followResults = await db
          .select()
          .from(follows)
          .where(
            and(
              eq(follows.followerId, currentUserId),
              eq(follows.followingId, post.userId),
              eq(follows.status, 'accepted')
            )
          )
          .limit(1);
        
        if (followResults.length === 0) {
          throw new ForbiddenError('You do not have permission to view this post');
        }
      } else {
        throw new ForbiddenError('You do not have permission to view this post');
      }
    }
    
    // Check if current user liked the post
    let isLiked = false;
    
    if (currentUserId) {
      const likeResults = await db
        .select()
        .from(likes)
        .where(
          and(
            eq(likes.postId, id),
            eq(likes.userId, currentUserId)
          )
        )
        .limit(1);
      
      isLiked = likeResults.length > 0;
    }
    
    return {
      ...post,
      isLiked
    };
  },
  
  /**
   * Update a post
   */
  async update(id: string, updateData: UpdatePostData, currentUserId: string) {
    // Verify post exists and belongs to user
    const postResults = await db
      .select()
      .from(posts)
      .where(eq(posts.id, id))
      .limit(1);
    
    if (postResults.length === 0) {
      throw new NotFoundError('Post not found');
    }
    
    const post = postResults[0];
    
    if (post.userId !== currentUserId) {
      throw new ForbiddenError('You do not have permission to update this post');
    }
    
    // Update the post
    await db.update(posts)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(posts.id, id));
    
    // Return the updated post
    return this.findById(id, currentUserId);
  },
  
  /**
   * Delete a post
   */
  async delete(id: string, currentUserId: string) {
    // Verify post exists and belongs to user
    const postResults = await db
      .select()
      .from(posts)
      .where(eq(posts.id, id))
      .limit(1);
    
    if (postResults.length === 0) {
      throw new NotFoundError('Post not found');
    }
    
    const post = postResults[0];
    
    if (post.userId !== currentUserId) {
      throw new ForbiddenError('You do not have permission to delete this post');
    }
    
    // Delete the post
    await db.delete(posts).where(eq(posts.id, id));
    
    // Update user posts count
    await db.update(users)
      .set({ postsCount: sql`${users.postsCount} - 1` })
      .where(eq(users.id, currentUserId));
    
    return { success: true };
  },
  
  /**
   * Like a post
   */
  async likePost(postId: string, userId: string) {
    // Verify post exists
    const postResults = await db.select().from(posts).where(eq(posts.id, postId)).limit(1);
    
    if (postResults.length === 0) {
      throw new NotFoundError('Post not found');
    }
    
    // Check if already liked
    const existingLikeResults = await db
      .select()
      .from(likes)
      .where(
        and(
          eq(likes.postId, postId),
          eq(likes.userId, userId)
        )
      )
      .limit(1);
    
    if (existingLikeResults.length > 0) {
      return { already_liked: true };
    }
    
    // Create like
    await db.insert(likes).values({
      id: uuidv4(),
      postId,
      userId,
      createdAt: new Date()
    });
    
    // Update post likes count
    await db.update(posts)
      .set({ likesCount: sql`${posts.likesCount} + 1` })
      .where(eq(posts.id, postId));
    
    return { success: true };
  },
  
  /**
   * Unlike a post
   */
  async unlikePost(postId: string, userId: string) {
    // Verify like exists
    const likeResults = await db
      .select()
      .from(likes)
      .where(
        and(
          eq(likes.postId, postId),
          eq(likes.userId, userId)
        )
      )
      .limit(1);
    
    if (likeResults.length === 0) {
      throw new NotFoundError('Like not found');
    }
    
    // Remove like
    await db
      .delete(likes)
      .where(
        and(
          eq(likes.postId, postId),
          eq(likes.userId, userId)
        )
      );
    
    // Update post likes count
    await db.update(posts)
      .set({ likesCount: sql`GREATEST(${posts.likesCount} - 1, 0)` })
      .where(eq(posts.id, postId));
    
    return { success: true };
  },
  
  /**
   * List posts with various filters
   */
  async listPosts({ 
    page = 1, 
    limit = 20, 
    userId = null,
    currentUserId = null
  }: {
    page?: number;
    limit?: number;
    userId?: string | null;
    currentUserId?: string | null;
  }) {
    const offset = (page - 1) * limit;
    
    // Build where conditions
    const whereConditions = [isNull(posts.deletedAt)];

    // Filter by user if specified
    if (userId) {
      whereConditions.push(eq(posts.userId, userId));
    }

    // Handle privacy
    if (currentUserId !== userId) {
      whereConditions.push(eq(posts.isPublic, true));
    }

    const results = await db
      .select({
        id: posts.id,
        content: posts.content,
        mediaUrls: posts.mediaUrls,
        likesCount: posts.likesCount,
        commentsCount: posts.commentsCount,
        sharesCount: posts.sharesCount,
        isPublic: posts.isPublic,
        createdAt: posts.createdAt,
        userId: posts.userId,
        username: users.username,
        displayName: users.displayName,
        profileImageUrl: users.profileImageUrl,
        isVerified: users.isVerified
      })
      .from(posts)
      .innerJoin(users, eq(posts.userId, users.id))
      .where(and(...whereConditions))
      .orderBy(desc(posts.createdAt))
      .limit(limit)
      .offset(offset);
    
    // Add isLiked field if currentUserId is provided
    let enhancedResults = results;
    
    if (currentUserId) {
      const postIds = results.map(post => post.id);
      
      // Get all likes for these posts from the current user
      const userLikes = await db
        .select()
        .from(likes)
        .where(and(
          eq(likes.userId, currentUserId),
          sql`${likes.postId} IN (${postIds.join(',')})`
        ));
      
      // Create a map for faster lookups
      const likedPostMap = new Map(userLikes.map(like => [like.postId, true]));
      
      enhancedResults = results.map(post => ({
        ...post,
        isLiked: likedPostMap.has(post.id)
      }));
    }
    
    return enhancedResults;
  },
  
  /**
   * Get posts by hashtag
   */
  async getPostsByHashtag(tag: string, page = 1, limit = 20, currentUserId?: string) {
    const offset = (page - 1) * limit;
    
    // Find hashtag
    const hashtagResults = await db
      .select()
      .from(hashtags)
      .where(eq(hashtags.name, tag))
      .limit(1);
    
    if (hashtagResults.length === 0) {
      return [];
    }
    
    const hashtagId = hashtagResults[0].id;
    
    // Get posts with this hashtag
    const results = await db
      .select({
        id: posts.id,
        content: posts.content,
        mediaUrls: posts.mediaUrls,
        likesCount: posts.likesCount,
        commentsCount: posts.commentsCount,
        sharesCount: posts.sharesCount,
        isPublic: posts.isPublic,
        createdAt: posts.createdAt,
        userId: posts.userId,
        username: users.username,
        displayName: users.displayName,
        profileImageUrl: users.profileImageUrl,
        isVerified: users.isVerified
      })
      .from(postHashtags)
      .innerJoin(posts, eq(postHashtags.postId, posts.id))
      .innerJoin(users, eq(posts.userId, users.id))
      .where(
        and(
          eq(postHashtags.hashtagId, hashtagId),
          isNull(posts.deletedAt),
          posts.isPublic
        )
      )
      .orderBy(desc(posts.createdAt))
      .limit(limit)
      .offset(offset);
    
    // Add isLiked field if currentUserId is provided
    let enhancedResults = results;
    
    if (currentUserId) {
      const postIds = results.map(post => post.id);
      
      if (postIds.length > 0) {
        // Get all likes for these posts from the current user
        const userLikes = await db
          .select()
          .from(likes)
          .where(and(
            eq(likes.userId, currentUserId),
            sql`${likes.postId} IN (${postIds.join(',')})`
          ));
        
        // Create a map for faster lookups
        const likedPostMap = new Map(userLikes.map(like => [like.postId, true]));
        
        enhancedResults = results.map(post => ({
          ...post,
          isLiked: likedPostMap.has(post.id)
        }));
      }
    }
    
    return enhancedResults;
  }
};