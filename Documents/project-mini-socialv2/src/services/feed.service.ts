import { and, desc, eq, isNull, not, or } from 'drizzle-orm';
import { db } from '../db';
import { follows, posts, users, hashtags, postHashtags } from '../db/schema';

// Feed Service Implementation
export const feedService = {
  /**
   * Get feed for a user
   */
  async getUserFeed(userId: string, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    // Get IDs of users that the current user follows
    const followingResults = await db
      .select({ followingId: follows.followingId })
      .from(follows)
      .where(
        and(
          eq(follows.followerId, userId),
          eq(follows.status, 'accepted')
        )
      );
    
    const followingIds = followingResults.map(follow => follow.followingId);
    
    // Include the user's own posts
    followingIds.push(userId);
    
    // Get posts from followed users and the user's own posts
    const results = await db
      .select({
        id: posts.id,
        content: posts.content,
        mediaUrls: posts.mediaUrls,
        likesCount: posts.likesCount,
        commentsCount: posts.commentsCount,
        sharesCount: posts.sharesCount,
        isPublic: posts.isPublic,
        createdAt: posts.createdAt,
        userId: posts.userId,
        username: users.username,
        displayName: users.displayName,
        profileImageUrl: users.profileImageUrl,
        isVerified: users.isVerified
      })
      .from(posts)
      .innerJoin(users, eq(posts.userId, users.id))
      .where(
        and(
          posts.userId.in(followingIds),
          isNull(posts.deletedAt),
          or(
            eq(posts.isPublic, true),
            posts.userId.in(followingIds)
          )
        )
      )
      .orderBy(desc(posts.createdAt))
      .limit(limit)
      .offset(offset);
    
    return results;
  },
  
  /**
   * Get explore feed (posts from users not followed)
   */
  async getExploreFeed(userId: string, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    // Get IDs of users that the current user follows
    const followingResults = await db
      .select({ followingId: follows.followingId })
      .from(follows)
      .where(
        and(
          eq(follows.followerId, userId),
          eq(follows.status, 'accepted')
        )
      );
    
    const followingIds = followingResults.map(follow => follow.followingId);
    
    // Include the user's own ID to exclude their posts as well
    followingIds.push(userId);
    
    // Get popular public posts from users the current user doesn't follow
    const results = await db
      .select({
        id: posts.id,
        content: posts.content,
        mediaUrls: posts.mediaUrls,
        likesCount: posts.likesCount,
        commentsCount: posts.commentsCount,
        sharesCount: posts.sharesCount,
        isPublic: posts.isPublic,
        createdAt: posts.createdAt,
        userId: posts.userId,
        username: users.username,
        displayName: users.displayName,
        profileImageUrl: users.profileImageUrl,
        isVerified: users.isVerified
      })
      .from(posts)
      .innerJoin(users, eq(posts.userId, users.id))
      .where(
        and(
          not(posts.userId.in(followingIds)),
          isNull(posts.deletedAt),
          eq(posts.isPublic, true)
        )
      )
      .orderBy(desc(posts.likesCount))
      .limit(limit)
      .offset(offset);
    
    return results;
  },
  
  /**
   * Get trending hashtags
   */
  async getTrendingHashtags(limit = 10) {
    // Get the most popular hashtags based on posts count
    const results = await db
      .select({
        id: hashtags.id,
        name: hashtags.name,
        postsCount: hashtags.postsCount
      })
      .from(hashtags)
      .orderBy(desc(hashtags.postsCount))
      .limit(limit);
    
    return results;
  },
  
  /**
   * Get posts by hashtag
   */
  async getPostsByHashtag(hashtagName: string, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    // Find hashtag ID
    const hashtagResults = await db
      .select()
      .from(hashtags)
      .where(eq(hashtags.name, hashtagName))
      .limit(1);
    
    if (hashtagResults.length === 0) {
      return [];
    }
    
    const hashtagId = hashtagResults[0].id;
    
    // Get posts with this hashtag
    const results = await db
      .select({
        id: posts.id,
        content: posts.content,
        mediaUrls: posts.mediaUrls,
        likesCount: posts.likesCount,
        commentsCount: posts.commentsCount,
        sharesCount: posts.sharesCount,
        isPublic: posts.isPublic,
        createdAt: posts.createdAt,
        userId: posts.userId,
        username: users.username,
        displayName: users.displayName,
        profileImageUrl: users.profileImageUrl,
        isVerified: users.isVerified
      })
      .from(postHashtags)
      .innerJoin(posts, eq(postHashtags.postId, posts.id))
      .innerJoin(users, eq(posts.userId, users.id))
      .where(
        and(
          eq(postHashtags.hashtagId, hashtagId),
          isNull(posts.deletedAt),
          eq(posts.isPublic, true)
        )
      )
      .orderBy(desc(posts.createdAt))
      .limit(limit)
      .offset(offset);
    
    return results;
  }
};