import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { UnauthorizedError, ForbiddenError } from '../utils/errors';

// Extend the Express Request interface
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role?: string;
      };
    }
  }
}

// Authentication middleware
export const authenticate = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new UnauthorizedError('Authentication token is required');
  }
  
  const token = authHeader.split(' ')[1];
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_ACCESS_SECRET as string) as {
      id: string;
      role?: string;
    };
    
    req.user = decoded;
    next();
  } catch (error) {
    throw new UnauthorizedError('Invalid or expired token');
  }
};

// Role-based authorization middleware
export const authorize = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new UnauthorizedError('User not authenticated');
    }
    
    if (!roles.includes(req.user.role || 'user')) {
      throw new ForbiddenError('You do not have permission to perform this action');
    }
    
    next();
  };
};

// Resource owner authorization middleware
export const isResourceOwner = (
  resourceIdParam: string,
  resourceType: 'user' | 'post' | 'comment'
) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new UnauthorizedError('User not authenticated');
    }
    
    const resourceId = req.params[resourceIdParam];
    
    // For user resources, directly compare IDs
    if (resourceType === 'user' && resourceId === req.user.id) {
      return next();
    }
    
    // For posts and comments, we would need to check ownership
    // This would typically involve a database query to verify ownership
    // For now, we'll implement this in a simple way and expand later
    
    // This would be replaced with actual service calls in a real implementation
    const isOwner = false; // Placeholder for actual ownership check
    
    if (!isOwner && req.user.role !== 'admin') {
      throw new ForbiddenError('You do not have permission to access this resource');
    }
    
    next();
  };
};