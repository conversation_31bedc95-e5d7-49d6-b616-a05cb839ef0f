import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { authService } from '../services/auth.service';
import { db } from '../db';
import { users } from '../db/schema';
import { BadRequestError, UnauthorizedError } from '../utils/errors';

// Mock the database functions
jest.mock('../db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnValue([]),
    insert: jest.fn().mockReturnValue({ returning: jest.fn().mockResolvedValue([]) }),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis()
  }
}));

// Mock bcrypt
jest.mock('bcrypt', () => ({
  genSalt: jest.fn().mockResolvedValue('salt'),
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true)
}));

// Mock jwt
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn().mockReturnValue('token'),
  verify: jest.fn().mockReturnValue({ id: '123', role: 'user' })
}));

describe('Auth Service', () => {
  const mockUser = {
    id: '123',
    username: 'testuser',
    email: '<EMAIL>',
    passwordHash: 'hashed_password',
    role: 'user',
    isActive: true
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('register', () => {
    it('should register a new user successfully', async () => {
      // Set up mocks for a successful registration
      const selectMock = jest.fn().mockReturnThis();
      const fromMock = jest.fn().mockReturnThis();
      const whereMock = jest.fn().mockReturnThis();
      const limitMock = jest.fn().mockReturnValue([]);
      
      (db.select as jest.Mock).mockImplementation(() => ({
        from: fromMock,
        where: whereMock,
        limit: limitMock
      }));
      
      const insertMock = jest.fn().mockResolvedValue({});
      (db.insert as jest.Mock).mockImplementation(() => insertMock);
      
      const result = await authService.register({
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123'
      });
      
      // Verify the result
      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('username', 'newuser');
      expect(result).toHaveProperty('email', '<EMAIL>');
      
      // Verify database calls
      expect(db.select).toHaveBeenCalled();
      expect(db.insert).toHaveBeenCalled();
      
      // Verify bcrypt calls
      expect(bcrypt.genSalt).toHaveBeenCalled();
      expect(bcrypt.hash).toHaveBeenCalled();
    });
    
    it('should throw an error if email is already in use', async () => {
      // Mock an existing user
      (db.select as jest.Mock).mockImplementation(() => ({
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnValue([mockUser])
      }));
      
      await expect(authService.register({
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123'
      })).rejects.toThrow(BadRequestError);
    });
  });
  
  describe('login', () => {
    it('should login a user successfully', async () => {
      // Mock finding the user
      (db.select as jest.Mock).mockImplementation(() => ({
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnValue([mockUser])
      }));
      
      const result = await authService.login({
        email: '<EMAIL>',
        password: 'password123'
      });
      
      // Verify the result
      expect(result).toHaveProperty('accessToken');
      expect(result).toHaveProperty('refreshToken');
      
      // Verify bcrypt calls
      expect(bcrypt.compare).toHaveBeenCalledWith('password123', 'hashed_password');
      
      // Verify jwt calls
      expect(jwt.sign).toHaveBeenCalledTimes(2);
    });
    
    it('should throw an error if user is not found', async () => {
      // Mock no user found
      (db.select as jest.Mock).mockImplementation(() => ({
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnValue([])
      }));
      
      await expect(authService.login({
        email: '<EMAIL>',
        password: 'password123'
      })).rejects.toThrow(UnauthorizedError);
    });
    
    it('should throw an error if password is invalid', async () => {
      // Mock finding the user
      (db.select as jest.Mock).mockImplementation(() => ({
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnValue([mockUser])
      }));
      
      // Mock password comparison failure
      (bcrypt.compare as jest.Mock).mockResolvedValueOnce(false);
      
      await expect(authService.login({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })).rejects.toThrow(UnauthorizedError);
    });
  });
  
  describe('refreshToken', () => {
    it('should refresh tokens successfully', async () => {
      // Mock finding the user
      (db.select as jest.Mock).mockImplementation(() => ({
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnValue([mockUser])
      }));
      
      const result = await authService.refreshToken('valid_refresh_token');
      
      // Verify the result
      expect(result).toHaveProperty('accessToken');
      expect(result).toHaveProperty('refreshToken');
      
      // Verify jwt calls
      expect(jwt.verify).toHaveBeenCalledWith('valid_refresh_token', expect.any(String));
      expect(jwt.sign).toHaveBeenCalledTimes(2);
    });
    
    it('should throw an error if token is invalid', async () => {
      // Mock token verification failure
      (jwt.verify as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Invalid token');
      });
      
      await expect(authService.refreshToken('invalid_token')).rejects.toThrow(UnauthorizedError);
    });
  });
});