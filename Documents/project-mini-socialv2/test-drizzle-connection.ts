import 'dotenv/config';
import { db } from './src/db';
import { users } from './src/db/schema';

async function testDrizzleConnection() {
  try {
    console.log('Testing Drizzle ORM connection...');
    console.log('DATABASE_URL:', process.env.DATABASE_URL);
    
    // Test simple query
    const result = await db.select().from(users).limit(1);
    console.log('✅ Drizzle ORM connection successful!');
    console.log('Query result:', result);
    
    // Test database version
    const versionResult = await db.execute('SELECT version()');
    console.log('Database version:', versionResult.rows[0]);
    
  } catch (error) {
    console.error('❌ Drizzle ORM connection failed:', error instanceof Error ? error.message : String(error));
    console.error('Error details:', error);
  }
}

testDrizzleConnection();
