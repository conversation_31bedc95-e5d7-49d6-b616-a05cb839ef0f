# Social Mini Post API

A production-ready Node.js/Express backend for a Social Mini Post application using TypeScript and Drizzle ORM with PostgreSQL.

## Features

- Complete authentication system with JWT and refresh tokens
- User management with follow relationships
- Post creation and interaction (likes, comments, sharing)
- Feed generation with personalized content
- Hashtag functionality
- Notifications system
- Reporting system for content moderation

## Tech Stack

- Node.js & Express
- TypeScript
- Drizzle ORM with PostgreSQL
- JWT Authentication
- Input validation with <PERSON><PERSON>
- <PERSON><PERSON><PERSON> handling middleware
- Logging with Winston
- Testing with Jest

## Project Structure

```
src/
├── controllers/       # Request handlers
├── services/          # Business logic
├── routes/            # API route definitions
├── db/                # Database configuration and schemas
├── middlewares/       # Express middlewares
├── validators/        # Request validation schemas
├── utils/             # Utility functions
└── tests/             # Test files
```

## API Endpoints

### Authentication
- POST /api/auth/register - Register a new user
- POST /api/auth/login - Login a user
- POST /api/auth/refresh-token - Refresh access token
- GET /api/auth/me - Get current user information

### Users
- GET /api/users/:id - Get user profile
- PUT /api/users/:id - Update user profile
- POST /api/users/:id/follow - Follow a user
- DELETE /api/users/:id/follow - Unfollow a user
- GET /api/users - Search users

### Posts
- POST /api/posts - Create a new post
- GET /api/posts - List posts with filters
- GET /api/posts/:id - Get a specific post
- PUT /api/posts/:id - Update a post
- DELETE /api/posts/:id - Delete a post
- POST /api/posts/:id/like - Like a post
- DELETE /api/posts/:id/like - Unlike a post
- POST /api/posts/:id/comments - Add a comment to a post
- GET /api/posts/:id/comments - Get comments for a post

### Comments
- PUT /api/comments/:id - Update a comment
- DELETE /api/comments/:id - Delete a comment
- POST /api/comments/:id/like - Like a comment
- DELETE /api/comments/:id/like - Unlike a comment
- GET /api/comments/:id/replies - Get replies to a comment

### Feed
- GET /api/feed - Get personalized feed
- GET /api/feed/explore - Get explore feed
- GET /api/feed/trending - Get trending hashtags
- GET /api/feed/hashtags/:tag/posts - Get posts by hashtag

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL

### Installation

1. Clone the repository
2. Install dependencies
   ```
   npm install
   ```
3. Create a `.env` file based on `.env.example` and fill in your configuration
4. Run database migrations
   ```
   npm run db:migrate
   ```
5. Start the development server
   ```
   npm run dev
   ```

## Testing

Run tests with:

```
npm test
```

## Deployment

Build the project with:

```
npm run build
```

Start the production server with:

```
npm start
```

## Docker

Build the Docker image:

```
docker build -t social-mini-post-api .
```

Run the container:

```
docker run -p 3000:3000 social-mini-post-api
```

## License

ISC