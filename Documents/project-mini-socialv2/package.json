{"name": "social-mini-post-backend", "version": "1.0.0", "description": "Backend API for Social Mini Post application", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "test": "jest --coverage", "test:watch": "jest --watch", "db:migrate": "ts-node src/db/migrate.ts", "db:seed": "ts-node src/db/seed.ts", "lint": "eslint src --ext .ts"}, "keywords": ["social", "api", "express", "typescript", "drizzle"], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.29.3", "express": "^4.19.2", "express-rate-limit": "^7.2.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "uuid": "^9.0.1", "winston": "^3.12.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.11.25", "@types/pg": "^8.10.9", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "drizzle-kit": "^0.20.14", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}