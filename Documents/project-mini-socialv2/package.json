{"name": "social-mini-post-backend", "version": "1.0.0", "description": "Backend API for Social Mini Post application", "main": "dist/server.js", "scripts": {"build": "rimraf dist && npx tsc", "start": "npm run build && node dist/server.js", "dev": "npm run build && concurrently \"npx tsc -w\" \"nodemon --exec ts-node src/server.ts\"", "test": "jest --coverage", "test:watch": "jest --watch", "db:migrate": "ts-node src/db/migrate.ts", "db:seed": "ts-node src/db/seed.ts", "lint": "eslint src --ext .ts"}, "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.1", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.16.0", "rimraf": "^6.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.25.48"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.29", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "drizzle-kit": "^0.31.1", "eslint": "^9.28.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3"}}