#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed. Please install Node.js and npm first."
        exit 1
    fi
    
    print_success "All prerequisites are installed."
}

# Clean and install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Clean previous installation
    if [ -d "node_modules" ]; then
        print_status "Removing existing node_modules..."
        rm -rf node_modules
    fi
    
    if [ -f "package-lock.json" ]; then
        print_status "Removing existing package-lock.json..."
        rm -f package-lock.json
    fi
    
    # Install dependencies
    print_status "Running npm install..."
    npm install
    
    if [ $? -eq 0 ]; then
        print_success "Dependencies installed successfully."
    else
        print_error "Failed to install dependencies."
        exit 1
    fi
}

# Setup Docker containers
setup_docker() {
    print_status "Setting up Docker containers..."
    
    # Stop and remove existing containers
    print_status "Stopping existing containers..."
    docker-compose down -v
    
    # Start containers
    print_status "Starting Docker containers..."
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        print_success "Docker containers started successfully."
    else
        print_error "Failed to start Docker containers."
        exit 1
    fi
}

# Wait for PostgreSQL to be ready
wait_for_postgres() {
    print_status "Waiting for PostgreSQL to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec project-mini-socialv2-postgres-1 pg_isready -U myuser -d mysocialdb >/dev/null 2>&1; then
            print_success "PostgreSQL is ready!"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts - PostgreSQL not ready yet, waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "PostgreSQL failed to start within expected time."
    exit 1
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # Generate migrations
    print_status "Generating migrations..."
    npm run db:generate
    
    if [ $? -ne 0 ]; then
        print_error "Failed to generate migrations."
        exit 1
    fi
    
    # Apply migrations using Docker exec (fallback method)
    print_status "Applying migrations via Docker..."
    
    # Copy migration files to container
    docker cp src/db/migrations project-mini-socialv2-postgres-1:/tmp/
    
    # Find the latest migration file
    MIGRATION_FILE=$(ls src/db/migrations/*.sql | head -1)
    MIGRATION_NAME=$(basename "$MIGRATION_FILE")
    
    if [ -f "$MIGRATION_FILE" ]; then
        print_status "Applying migration: $MIGRATION_NAME"
        docker exec project-mini-socialv2-postgres-1 psql -U myuser -d mysocialdb -f "/tmp/migrations/$MIGRATION_NAME"
        
        if [ $? -eq 0 ]; then
            print_success "Migrations applied successfully via Docker."
        else
            print_error "Failed to apply migrations via Docker."
            exit 1
        fi
    else
        print_error "No migration files found."
        exit 1
    fi
}

# Verify setup
verify_setup() {
    print_status "Verifying setup..."
    
    # Check if containers are running
    if ! docker ps | grep -q "project-mini-socialv2-postgres-1"; then
        print_error "PostgreSQL container is not running."
        exit 1
    fi
    
    # Check if database tables exist
    TABLE_COUNT=$(docker exec project-mini-socialv2-postgres-1 psql -U myuser -d mysocialdb -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')
    
    if [ "$TABLE_COUNT" -gt 0 ]; then
        print_success "Database setup verified. Found $TABLE_COUNT tables."
        
        # List tables
        print_status "Database tables:"
        docker exec project-mini-socialv2-postgres-1 psql -U myuser -d mysocialdb -c "\dt"
    else
        print_error "No tables found in database."
        exit 1
    fi
}

# Build application
build_app() {
    print_status "Building application..."
    
    npm run build
    
    if [ $? -eq 0 ]; then
        print_success "Application built successfully."
    else
        print_error "Failed to build application."
        exit 1
    fi
}

# Main execution
main() {
    echo "🚀 Starting Auto Migration Setup..."
    echo "=================================="
    
    check_prerequisites
    install_dependencies
    setup_docker
    wait_for_postgres
    run_migrations
    verify_setup
    build_app
    
    echo "=================================="
    print_success "🎉 Setup completed successfully!"
    echo ""
    print_status "Next steps:"
    echo "  • Run 'npm run dev' to start development server"
    echo "  • Access pgAdmin at http://localhost:5050"
    echo "  • PostgreSQL is running on localhost:5432"
    echo ""
    print_status "Database credentials:"
    echo "  • Host: localhost"
    echo "  • Port: 5432"
    echo "  • Database: mysocialdb"
    echo "  • Username: myuser"
    echo "  • Password: mypassword"
}

# Run main function
main "$@"
