{"info": {"_postman_id": "b5f3b0d2-4b8a-4e3c-9f3d-8b2e7f8d9e1a", "name": "Social Mini Post API", "description": "API collection for testing the Social Mini Post application endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"johndo<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Password123!\",\n    \"displayName\": \"<PERSON>\",\n    \"bio\": \"Software developer and tech enthusiast\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "pm.environment.set('accessToken', response.data.accessToken);", "pm.environment.set('refreshToken', response.data.refreshToken);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Password123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}}}]}, {"name": "Posts", "item": [{"name": "Create Post", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"content\": \"Hello world! #firstpost\",\n    \"mediaUrls\": [\"https://example.com/image.jpg\"],\n    \"isPublic\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/posts", "host": ["{{baseUrl}}"], "path": ["api", "posts"]}}}, {"name": "Get Posts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/posts?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "posts"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Post by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/posts/{{postId}}", "host": ["{{baseUrl}}"], "path": ["api", "posts", "{{postId}}"]}}}, {"name": "Update Post", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"content\": \"Updated post content! #edited\",\n    \"isPublic\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/posts/{{postId}}", "host": ["{{baseUrl}}"], "path": ["api", "posts", "{{postId}}"]}}}, {"name": "Like Post", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/posts/{{postId}}/like", "host": ["{{baseUrl}}"], "path": ["api", "posts", "{{postId}}", "like"]}}}]}, {"name": "Comments", "item": [{"name": "Create Comment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"content\": \"Great post!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/posts/{{postId}}/comments", "host": ["{{baseUrl}}"], "path": ["api", "posts", "{{postId}}", "comments"]}}}, {"name": "Get Comments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/posts/{{postId}}/comments?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "posts", "{{postId}}", "comments"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}]}, {"name": "Users", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"displayName\": \"<PERSON> Updated\",\n    \"bio\": \"Updated bio content\",\n    \"profileImageUrl\": \"https://example.com/profile.jpg\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}}, {"name": "Follow User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/follow", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "follow"]}}}]}, {"name": "Feed", "item": [{"name": "Get User Feed", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/feed?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "feed"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Posts by Hashtag", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/feed/hashtags/{{tag}}/posts?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "feed", "hashtags", "{{tag}}", "posts"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}]}]}