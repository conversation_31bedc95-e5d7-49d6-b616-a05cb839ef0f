{"id": "7f8e9d6c-5b4a-3c2d-1e0f-9a8b7c6d5e4f", "name": "Social Mini Post API - Local", "values": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "default", "enabled": true}, {"key": "accessToken", "value": "", "type": "secret", "enabled": true}, {"key": "refreshToken", "value": "", "type": "secret", "enabled": true}, {"key": "userId", "value": "", "type": "default", "enabled": true}, {"key": "postId", "value": "", "type": "default", "enabled": true}, {"key": "tag", "value": "firstpost", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-03-15T12:00:00.000Z", "_postman_exported_using": "Postman/10.24.3"}